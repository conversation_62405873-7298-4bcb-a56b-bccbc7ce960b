.hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  padding: 120px 0 80px;
  position: relative;
  overflow: hidden;
}

.hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
}

.hero-text {
  .hero-title {
    font-size: 48px;
    font-weight: 700;
    line-height: 1.2;
    color: #fff;
    margin-bottom: 20px;

    .highlight {
      color: #4285f4;
    }
  }

  .hero-description {
    font-size: 18px;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 40px;
    line-height: 1.6;
  }
}

.search-container {
  padding: 30px;
  margin-top: 20px;
}

.search-wrapper {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;

  .search-input {
    flex: 1;
    padding: 15px 20px;
    border: none;
    border-radius: 25px;
    font-size: 16px;
    background: rgba(255, 255, 255, 0.9);
    color: #333;
    outline: none;

    &::placeholder {
      color: #666;
    }

    &:focus {
      background: rgba(255, 255, 255, 1);
      box-shadow: 0 0 20px rgba(66, 133, 244, 0.3);
    }
  }

  .search-btn {
    padding: 15px 30px;
    white-space: nowrap;
  }
}

.search-filters {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;

  .filter-btn {
    padding: 8px 16px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    background: transparent;
    color: #fff;
    border-radius: 20px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover, &.active {
      background: rgba(255, 255, 255, 0.2);
      border-color: rgba(255, 255, 255, 0.5);
    }
  }
}

.hero-visual {
  display: flex;
  justify-content: center;
  align-items: center;
}

.job-card-preview {
  width: 100%;
  max-width: 400px;
  padding: 25px;

  .job-preview-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 20px;

    .job-icon {
      font-size: 24px;
      width: 50px;
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(66, 133, 244, 0.2);
      border-radius: 12px;
    }

    .job-info {
      h3 {
        color: #fff;
        font-size: 18px;
        font-weight: 600;
        margin: 0 0 5px 0;
      }

      p {
        color: rgba(255, 255, 255, 0.8);
        font-size: 14px;
        margin: 0;
      }
    }
  }

  .job-preview-stats {
    .stat {
      margin-bottom: 15px;

      .stat-value {
        display: block;
        color: #4285f4;
        font-size: 20px;
        font-weight: 600;
      }

      .stat-label {
        color: rgba(255, 255, 255, 0.8);
        font-size: 14px;
      }
    }

    .payment-methods {
      display: flex;
      align-items: center;
      gap: 10px;

      .payment-icon {
        font-size: 18px;
      }

      .verified {
        color: #34a853;
        font-size: 12px;
        font-weight: 500;
        margin-left: auto;
      }
    }
  }
}

@media (max-width: 768px) {
  .hero {
    padding: 100px 0 60px;
  }

  .hero-content {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }

  .hero-text {
    .hero-title {
      font-size: 36px;
    }

    .hero-description {
      font-size: 16px;
    }
  }

  .search-wrapper {
    flex-direction: column;

    .search-btn {
      align-self: stretch;
    }
  }

  .search-filters {
    justify-content: center;
  }
}