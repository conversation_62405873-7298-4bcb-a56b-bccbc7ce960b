.job-card {
  padding: 25px;
  margin-bottom: 20px;
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 45px 0 rgba(31, 38, 135, 0.5);
  }
}

.job-header {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 15px;

  .job-icon {
    width: 50px;
    height: 50px;
    background: rgba(66, 133, 244, 0.2);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
  }

  .job-info {
    flex: 1;

    .job-title {
      color: #333;
      font-size: 18px;
      font-weight: 600;
      margin: 0 0 5px 0;
      line-height: 1.3;
    }

    .job-company {
      color: #666;
      font-size: 14px;
      margin: 0;
    }
  }
}

.job-location {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
  font-size: 14px;
  margin-bottom: 15px;

  .location-icon {
    font-size: 16px;
  }
}

.job-description {
  color: #555;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 20px;
}

.job-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  .job-salary {
    .salary-amount {
      color: #4285f4;
      font-size: 18px;
      font-weight: 600;
    }

    .salary-period {
      color: #666;
      font-size: 14px;
      margin-left: 5px;
    }
  }

  .job-date {
    display: flex;
    align-items: center;
    gap: 5px;
    color: #888;
    font-size: 12px;

    .date-icon {
      font-size: 14px;
    }
  }
}

.job-actions {
  .btn-details {
    background: transparent;
    border: 1px solid rgba(66, 133, 244, 0.5);
    color: #4285f4;
    padding: 10px 20px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;

    &:hover {
      background: rgba(66, 133, 244, 0.1);
      border-color: #4285f4;
      transform: translateY(-2px);
    }
  }
}

@media (max-width: 768px) {
  .job-card {
    padding: 20px;
  }

  .job-header {
    .job-icon {
      width: 40px;
      height: 40px;
      font-size: 20px;
    }

    .job-info {
      .job-title {
        font-size: 16px;
      }
    }
  }

  .job-details {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}