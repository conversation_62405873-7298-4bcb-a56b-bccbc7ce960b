<section class="job-carousel">
  <div class="container">
    <div class="section-header">
      <h2 class="section-title">Izd<PERSON><PERSON><PERSON> poslovi</h2>
    </div>

    <div class="carousel-container">
      <div class="carousel-track" [style.transform]="'translateX(' + translateX + 'px)'">
        <div class="carousel-slide" *ngFor="let job of jobs; let i = index">
          <app-job-card [job]="job"></app-job-card>
        </div>
      </div>

      <div class="carousel-indicators">
        <button
          class="indicator"
          *ngFor="let job of jobs; let i = index"
          [class.active]="i === currentSlide"
          (click)="goToSlide(i)"
        ></button>
      </div>
    </div>

    <div class="carousel-footer">
      <button class="btn-primary view-all-btn" (click)="viewAllJobs()">
        Pogledaj sve poslove →
      </button>
    </div>
  </div>
</section>
