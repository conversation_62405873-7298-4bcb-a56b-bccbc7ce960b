.stats {
  padding: 80px 0;
  margin-top: -40px;
  position: relative;
  z-index: 10;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
  max-width: 1000px;
  margin: 0 auto;
}

.stat-item {
  text-align: center;
  padding: 40px 20px;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-10px);
  }

  .stat-number {
    font-size: 48px;
    font-weight: 700;
    color: #4285f4;
    margin-bottom: 10px;
    line-height: 1;
  }

  .stat-label {
    font-size: 16px;
    color: #fff;
    font-weight: 500;
    opacity: 0.9;
  }
}

@media (max-width: 768px) {
  .stats {
    padding: 60px 0;
    margin-top: -20px;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  .stat-item {
    padding: 30px 15px;

    .stat-number {
      font-size: 36px;
    }

    .stat-label {
      font-size: 14px;
    }
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
}