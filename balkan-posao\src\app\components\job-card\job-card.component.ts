import { Component, Input } from '@angular/core';

export interface Job {
  id: number;
  title: string;
  company: string;
  location: string;
  description: string;
  salary: string;
  period: string;
  date: string;
  icon: string;
}

@Component({
  selector: 'app-job-card',
  imports: [],
  templateUrl: './job-card.component.html',
  styleUrl: './job-card.component.scss'
})
export class JobCardComponent {
  @Input() job!: Job;

  viewDetails() {
    console.log('Viewing details for job:', this.job.id);
    // Implement navigation to job details
  }
}
