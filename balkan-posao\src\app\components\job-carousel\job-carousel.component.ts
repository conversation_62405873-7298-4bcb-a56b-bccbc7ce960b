import { Component, OnInit, OnD<PERSON>roy, Inject, PLATFORM_ID } from '@angular/core';
import { CommonModule, isPlatformBrowser } from '@angular/common';
import { JobCardComponent, Job } from '../job-card/job-card.component';

@Component({
  selector: 'app-job-carousel',
  imports: [CommonModule, JobCardComponent],
  templateUrl: './job-carousel.component.html',
  styleUrl: './job-carousel.component.scss'
})
export class JobCarouselComponent implements OnInit, OnDestroy {
  currentSlide = 0;
  translateX = 0;
  slideWidth = 350; // Width of each slide including gap
  autoSlideInterval: any;
  private isBrowser: boolean;

  jobs: Job[] = [
    {
      id: 1,
      title: 'Medicinska sestra',
      company: 'Klinika München',
      location: 'München, Njemačka',
      description: 'Tražimo medicinske sestre sa iskustvom za rad u modernoj klinici. Sigurna pozicija.',
      salary: '3.200 €',
      period: '- 3.500 €',
      date: '15.06.2023',
      icon: '🏥'
    },
    {
      id: 2,
      title: 'Električar',
      company: 'BauTech GmbH',
      location: 'Berlin, Njemačka',
      description: 'Potrebni električari sa iskustvom za rad na velikim građevinskim projektima. Odlični uslovi rada.',
      salary: '2.800 €',
      period: '- 3.500 €',
      date: '20.06.2023',
      icon: '⚡'
    },
    {
      id: 3,
      title: 'Vozač kamiona',
      company: 'LogiTrans GmbH',
      location: 'Hamburg, Njemačka',
      description: 'Tražimo vozače C i E kategorije za međunarodni transport. Odlični uslovi i redovna primanja.',
      salary: '3.000 €',
      period: '- 3.800 €',
      date: '10.06.2023',
      icon: '🚛'
    },
    {
      id: 4,
      title: 'Kuhar',
      company: 'Restaurant Adler',
      location: 'Frankfurt, Njemačka',
      description: 'Restoran traži kuhara sa iskustvom u pripremi internacionalnih jela. Smještaj osiguran.',
      salary: '2.600 €',
      period: '- 3.200 €',
      date: '25.06.2023',
      icon: '👨‍🍳'
    }
  ];

  constructor(@Inject(PLATFORM_ID) platformId: Object) {
    this.isBrowser = isPlatformBrowser(platformId);
  }

  ngOnInit() {
    this.updateTranslateX();
    this.startAutoSlide();

    // Listen for window resize only in browser
    if (this.isBrowser) {
      window.addEventListener('resize', () => {
        this.updateTranslateX();
      });
    }
  }

  ngOnDestroy() {
    if (this.autoSlideInterval) {
      clearInterval(this.autoSlideInterval);
    }

    // Remove resize listener only in browser
    if (this.isBrowser) {
      window.removeEventListener('resize', () => {
        this.updateTranslateX();
      });
    }
  }

  startAutoSlide() {
    // Only start auto slide in browser
    if (this.isBrowser) {
      this.autoSlideInterval = setInterval(() => {
        this.nextSlide();
      }, 5000); // Change slide every 5 seconds
    }
  }

  nextSlide() {
    this.currentSlide = (this.currentSlide + 1) % this.jobs.length;
    this.updateTranslateX();
  }

  goToSlide(index: number) {
    this.currentSlide = index;
    this.updateTranslateX();

    // Reset auto slide timer
    if (this.autoSlideInterval) {
      clearInterval(this.autoSlideInterval);
      this.startAutoSlide();
    }
  }

  updateTranslateX() {
    // Adjust slide width based on screen size only in browser
    if (this.isBrowser) {
      if (window.innerWidth <= 480) {
        this.slideWidth = 280;
      } else if (window.innerWidth <= 768) {
        this.slideWidth = 320;
      } else {
        this.slideWidth = 350;
      }
    }

    this.translateX = -this.currentSlide * this.slideWidth;
  }

  viewAllJobs() {
    console.log('Viewing all jobs');
    // Implement navigation to all jobs page
  }
}
