.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  padding: 15px 0;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 60px;
}

.navbar-brand {
  .logo {
    text-decoration: none;
    font-size: 28px;
    font-weight: 700;

    .logo-text {
      color: #fff;
    }

    .logo-accent {
      color: #4285f4;
    }
  }
}

.navbar-nav {
  display: flex;
  list-style: none;
  gap: 30px;
  margin: 0;
  padding: 0;

  .nav-item {
    .nav-link {
      color: #fff;
      text-decoration: none;
      font-weight: 500;
      font-size: 16px;
      transition: all 0.3s ease;
      padding: 8px 16px;
      border-radius: 20px;

      &:hover {
        background: rgba(255, 255, 255, 0.1);
        transform: translateY(-2px);
      }
    }
  }
}

.navbar-toggle {
  display: none;
  flex-direction: column;
  cursor: pointer;
  gap: 4px;

  .bar {
    width: 25px;
    height: 3px;
    background: #fff;
    border-radius: 2px;
    transition: all 0.3s ease;
  }
}

.mobile-menu {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-radius: 0 0 15px 15px;
  padding: 20px;
  transform: translateY(-100%);
  opacity: 0;
  transition: all 0.3s ease;

  &.active {
    transform: translateY(0);
    opacity: 1;
  }

  .mobile-nav {
    list-style: none;
    margin: 0;
    padding: 0;

    li {
      margin-bottom: 15px;

      .mobile-nav-link {
        color: #333;
        text-decoration: none;
        font-weight: 500;
        font-size: 18px;
        display: block;
        padding: 10px;
        border-radius: 10px;
        transition: all 0.3s ease;

        &:hover {
          background: rgba(66, 133, 244, 0.1);
          color: #4285f4;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .navbar-nav {
    display: none;
  }

  .navbar-toggle {
    display: flex;
  }

  .mobile-menu {
    display: block;
  }
}