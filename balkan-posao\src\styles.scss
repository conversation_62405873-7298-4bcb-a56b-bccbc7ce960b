/* Global styles for BalkanPosao */

// Reset and base styles
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Noto Sans', sans-serif;
  line-height: 1.6;
  color: #333;
  background: #ffffff;
  min-height: 100vh;
  position: relative;
}

// Subtle purple background elements for glassmorphism effect
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
  pointer-events: none;
  z-index: -1;
}

// Glassmorphism utility classes
.glass {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 10px;
  border: 1px solid rgba(102, 126, 234, 0.1);
  box-shadow: 0 8px 32px 0 rgba(102, 126, 234, 0.1);
}

.glass-dark {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 10px;
  border: 1px solid rgba(102, 126, 234, 0.15);
  box-shadow: 0 8px 32px 0 rgba(102, 126, 234, 0.15);
}

.glass-card {
  background: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  border-radius: 15px;
  border: 1px solid rgba(102, 126, 234, 0.1);
  box-shadow: 0 8px 32px 0 rgba(102, 126, 234, 0.1);
  padding: 20px;
  transition: all 0.3s ease;
}

.glass-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 40px 0 rgba(102, 126, 234, 0.2);
}

// Button styles
.btn-primary {
  background: #4285f4;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 25px;
  font-family: 'Noto Sans', sans-serif;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
}

.btn-primary:hover {
  background: #3367d6;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(66, 133, 244, 0.3);
}

// Container
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

// Additional glassmorphism variants
.glass-light {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border-radius: 12px;
  border: 1px solid rgba(102, 126, 234, 0.08);
  box-shadow: 0 8px 32px 0 rgba(102, 126, 234, 0.08);
}

.glass-strong {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(25px);
  -webkit-backdrop-filter: blur(25px);
  border-radius: 20px;
  border: 1px solid rgba(102, 126, 234, 0.12);
  box-shadow: 0 12px 40px 0 rgba(102, 126, 234, 0.12);
}

// Purple accent elements
.purple-accent {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
}

// Smooth scrolling
html {
  scroll-behavior: smooth;
}

// Custom scrollbar
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

::-webkit-scrollbar-thumb {
  background: rgba(66, 133, 244, 0.5);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(66, 133, 244, 0.8);
}

// Loading animation
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

// Pulse animation for buttons
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(66, 133, 244, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(66, 133, 244, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(66, 133, 244, 0);
  }
}

.pulse {
  animation: pulse 2s infinite;
}

// Responsive utilities
@media (max-width: 768px) {
  .container {
    padding: 0 15px;
  }

  body {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 10px;
  }
}
