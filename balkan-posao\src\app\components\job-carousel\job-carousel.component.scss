.job-carousel {
  padding: 80px 0;
  background: rgba(102, 126, 234, 0.02);
}

.section-header {
  text-align: center;
  margin-bottom: 50px;

  .section-title {
    font-size: 36px;
    font-weight: 700;
    color: #333;
    margin: 0;
  }
}

.carousel-container {
  position: relative;
  overflow: hidden;
  margin-bottom: 40px;
}

.carousel-track {
  display: flex;
  transition: transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  gap: 30px;
}

.carousel-slide {
  min-width: 320px;
  flex-shrink: 0;
}

.carousel-indicators {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-top: 30px;

  .indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: none;
    background: rgba(102, 126, 234, 0.2);
    cursor: pointer;
    transition: all 0.3s ease;

    &.active {
      background: #4285f4;
      transform: scale(1.2);
    }

    &:hover {
      background: rgba(102, 126, 234, 0.4);
    }
  }
}

.carousel-footer {
  text-align: center;
  margin-top: 40px;

  .view-all-btn {
    padding: 15px 40px;
    font-size: 16px;
    font-weight: 600;
  }
}

@media (max-width: 768px) {
  .job-carousel {
    padding: 60px 0;
  }

  .section-header {
    margin-bottom: 30px;

    .section-title {
      font-size: 28px;
    }
  }

  .carousel-track {
    gap: 20px;
  }

  .carousel-slide {
    min-width: 280px;
  }

  .carousel-footer {
    margin-top: 30px;

    .view-all-btn {
      padding: 12px 30px;
      font-size: 14px;
    }
  }
}

@media (max-width: 480px) {
  .carousel-slide {
    min-width: 260px;
  }
}